<!--pages/building/list/buildingList.wxml-->
<!-- 自定义导航栏 -->
<custom-navbar title="选择楼宇" show-back="{{true}}" background-color="#2492F2" text-color="#ffffff" />

<view class="building-list-container">
  <!-- 搜索框 -->
  <view class="search-header">
    <view class="search-input-wrapper">
      <t-icon class="search-icon" name="search" size="32rpx" color="#999999" />
      <input
        class="search-input"
        placeholder="搜索楼宇名称或地址"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
        confirm-type="search"
        placeholder-class="search-placeholder"
      />
      <t-icon
        class="clear-icon"
        name="close-circle-filled"
        size="32rpx"
        color="#cccccc"
        wx:if="{{searchKeyword}}"
        bind:tap="clearSearch"
      />
    </view>
  </view>

  <!-- 当前楼宇显示 -->
  <view class="current-building" wx:if="{{currentBuildingName}}">
    <text class="current-building-text">当前楼宇：{{currentBuildingName}}</text>
  </view>
  <!-- 楼宇列表 -->
  <scroll-view
    class="building-scroll"
    scroll-y
    scroll-into-view="{{scrollIntoView}}"
    style="height: calc(100vh - {{currentBuildingName ? '260rpx' : '200rpx'}});"
  >
    <view wx:if="{{filteredBuildingList.length === 0}}" class="empty-state">
      <t-icon name="{{searchKeyword ? 'search' : 'location'}}" size="120rpx" color="#cccccc" />
      <text class="empty-text">{{searchKeyword ? '未找到相关楼宇' : '暂无楼宇数据'}}</text>
      <text class="empty-tip" wx:if="{{searchKeyword}}">试试其他关键词吧</text>
    </view>

    <view wx:else class="building-list-content">
      <view
        wx:for="{{filteredBuildingList}}"
        wx:key="alphabet"
        class="alphabet-section"
        id="alphabet-{{item.alphabet}}"
      >
        <!-- 字母分组标题 -->
        <view class="alphabet-title">{{item.alphabet}}</view>

        <!-- 该字母下的楼宇列表 -->
        <view
          wx:for="{{item.buildings}}"
          wx:for-item="building"
          wx:key="id"
          class="building-item {{selectedBuildingId === building.id ? 'selected' : ''}}"
          data-building="{{building}}"
          bind:tap="onBuildingSelect"
        >
          <view class="building-info">
            <view class="building-name">{{building.name}}</view>
            <view class="building-address" wx:if="{{building.address}}">{{building.address}}</view>
            <view class="building-distance" wx:if="{{building.distanceText}}">
              <t-icon name="location" size="24rpx" color="#999999" />
              <text>距离 {{building.distanceText}}</text>
            </view>
          </view>
          <view class="building-check" wx:if="{{selectedBuildingId === building.id}}">
            <t-icon name="check-circle-filled" size="40rpx" color="#2492F2" />
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>

<!-- Toast提示 -->
<t-toast id="t-toast" />
