// pages/building/list/buildingList.js
import BCApi from '~/api/BCApi';
// import { findNearestBuilding } from '~/utils/locationUtils'; // 已隐藏距离计算逻辑
import Toast from 'tdesign-miniprogram/toast/index';

Page({
  data: {
    buildingList: [],
    filteredBuildingList: [],
    selectedBuildingId: '',
    currentLocation: null,
    searchKeyword: '',
    currentBuildingName: ''
  },

  onLoad(options) {
    // 获取当前选中的楼宇ID和名称
    const currentBuildingId = wx.getStorageSync('selectedBuildingId') || '';
    const currentBuildingName = wx.getStorageSync('selectedBuildingName') || '';
    this.setData({
      selectedBuildingId: currentBuildingId,
      currentBuildingName: currentBuildingName
    });

    this.loadBuildingList();
  },

  // 加载楼宇列表
  async loadBuildingList() {
    try {
      wx.showLoading({ title: '加载中...', mask: true });

      const res = await BCApi.BCList();
      console.log('BCList 接口返回数据:', res);

      // 兼容不同的数据结构
      let buildingList = [];
      if (res && res.records && Array.isArray(res.records)) {
        buildingList = res.records;
      } else if (res && Array.isArray(res)) {
        buildingList = res;
      } else if (res && res.data && Array.isArray(res.data)) {
        buildingList = res.data;
      } else {
        console.warn('BCList 接口返回数据格式异常:', res);
        buildingList = [];
      }

      console.log('处理后的楼宇列表:', buildingList);

      if (buildingList.length > 0) {
        // 隐藏距离计算逻辑，直接按首字母排序
        // await this.calculateDistances(buildingList); // 已注释距离计算

        // 按首字母排序
        const sortedList = this.sortBuildingsByAlphabet(buildingList);

        // 测试排序结果
        console.log('排序测试:');
        console.log('汇 -> ', this.getFirstLetter('汇'));
        console.log('长 -> ', this.getFirstLetter('长'));
        console.log('兆 -> ', this.getFirstLetter('兆'));

        this.setData({
          buildingList: buildingList,
          filteredBuildingList: sortedList
        });
      } else {
        this.setData({
          buildingList: [],
          filteredBuildingList: []
        });
      }

      wx.hideLoading();
    } catch (error) {
      console.error('加载楼宇列表失败:', error);
      wx.hideLoading();
      this.setData({
        buildingList: [],
        filteredBuildingList: []
      });
      Toast({
        context: this,
        selector: '#t-toast',
        message: '加载楼宇列表失败',
        icon: 'error',
        duration: 2000
      });
    }
  },

  // 距离计算相关方法已隐藏（避免wx.getLocation审核问题）
  /*
  // 计算楼宇距离
  async calculateDistances(buildingList) {
    // 已注释，隐藏距离计算逻辑
  },

  // 获取当前位置
  getCurrentLocation() {
    // 已注释，隐藏wx.getLocation调用
  },

  // 计算距离（Haversine公式）
  calculateDistance(lat1, lng1, lat2, lng2) {
    // 已注释，隐藏距离计算逻辑
  },
  */

  // 按首字母排序楼宇
  sortBuildingsByAlphabet(buildingList) {
    // 检查输入参数
    if (!buildingList || !Array.isArray(buildingList) || buildingList.length === 0) {
      console.warn('楼宇列表为空或格式错误');
      return [];
    }

    const grouped = {};
    const alphabetList = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '#'];

    buildingList.forEach(building => {
      if (!building || !building.name) {
        console.warn('楼宇数据缺少名称:', building);
        return;
      }

      // 获取楼宇名称的首字母
      const firstChar = this.getFirstLetter(building.name);
      if (!grouped[firstChar]) {
        grouped[firstChar] = [];
      }
      grouped[firstChar].push(building);
    });

    // 转换为数组格式并排序
    const result = [];
    alphabetList.forEach(letter => {
      if (grouped[letter] && grouped[letter].length > 0) {
        result.push({
          alphabet: letter,
          buildings: grouped[letter].sort((a, b) => {
            if (!a || !a.name || !b || !b.name) return 0;
            return a.name.localeCompare(b.name);
          })
        });
      }
    });

    return result;
  },

  // 获取中文字符的首字母（使用更准确的拼音映射）
  getFirstLetter(str) {
    if (!str) return '#';

    const firstChar = str.charAt(0);

    // 如果是英文字母，直接返回大写
    if (/^[a-zA-Z]$/.test(firstChar)) {
      return firstChar.toUpperCase();
    }

    // 如果是数字，返回#
    if (/^[0-9]$/.test(firstChar)) {
      return '#';
    }

    // 详细的中文字符拼音首字母映射表
    const pinyinMap = {
      // A
      '啊': 'A', '阿': 'A', '安': 'A', '爱': 'A', '奥': 'A', '澳': 'A',
      // B
      '八': 'B', '白': 'B', '百': 'B', '北': 'B', '本': 'B', '比': 'B', '博': 'B', '宝': 'B', '保': 'B', '报': 'B', '贝': 'B',
      // C
      '才': 'C', '菜': 'C', '长': 'C', '常': 'C', '成': 'C', '城': 'C', '出': 'C', '春': 'C', '创': 'C', '超': 'C', '朝': 'C', '车': 'C', '晨': 'C', '昌': 'C', '场': 'C', '厂': 'C', '唱': 'C', '畅': 'C',
      // D
      '大': 'D', '代': 'D', '当': 'D', '道': 'D', '的': 'D', '地': 'D', '第': 'D', '东': 'D', '都': 'D', '德': 'D', '达': 'D', '丹': 'D',
      // E
      '二': 'E', '儿': 'E', '恩': 'E',
      // F
      '发': 'F', '法': 'F', '方': 'F', '房': 'F', '风': 'F', '福': 'F', '富': 'F', '飞': 'F', '凤': 'F', '峰': 'F',
      // G
      '高': 'G', '个': 'G', '工': 'G', '公': 'G', '国': 'G', '广': 'G', '光': 'G', '港': 'G', '贵': 'G', '管': 'G', '冠': 'G',
      // H
      '海': 'H', '好': 'H', '和': 'H', '河': 'H', '黑': 'H', '红': 'H', '华': 'H', '花': 'H', '环': 'H', '黄': 'H', '会': 'H', '汇': 'H', '恒': 'H', '宏': 'H', '弘': 'H', '鸿': 'H', '虹': 'H', '辉': 'H', '慧': 'H', '惠': 'H',
      // J
      '家': 'J', '建': 'J', '江': 'J', '金': 'J', '京': 'J', '九': 'J', '就': 'J', '佳': 'J', '嘉': 'J', '加': 'J', '吉': 'J', '集': 'J', '济': 'J', '继': 'J', '际': 'J', '景': 'J', '精': 'J', '晶': 'J', '静': 'J', '君': 'J', '骏': 'J', '兆': 'J',
      // K
      '开': 'K', '可': 'K', '科': 'K', '康': 'K', '凯': 'K',
      // L
      '来': 'L', '老': 'L', '乐': 'L', '里': 'L', '丽': 'L', '利': 'L', '立': 'L', '林': 'L', '龙': 'L', '路': 'L', '绿': 'L', '蓝': 'L', '朗': 'L', '联': 'L', '良': 'L', '亮': 'L', '力': 'L', '理': 'L', '丽': 'L', '励': 'L', '立': 'L',
      // M
      '马': 'M', '美': 'M', '门': 'M', '民': 'M', '明': 'M', '名': 'M', '茂': 'M', '梅': 'M', '米': 'M', '密': 'M', '敏': 'M',
      // N
      '南': 'N', '年': 'N', '宁': 'N', '农': 'N', '诺': 'N', '能': 'N',
      // O
      '欧': 'O',
      // P
      '平': 'P', '普': 'P', '鹏': 'P', '朋': 'P', '培': 'P',
      // Q
      '七': 'Q', '其': 'Q', '前': 'Q', '青': 'Q', '清': 'Q', '全': 'Q', '强': 'Q', '庆': 'Q', '秋': 'Q', '群': 'Q', '琼': 'Q',
      // R
      '人': 'R', '日': 'R', '如': 'R', '瑞': 'R', '荣': 'R', '融': 'R', '润': 'R', '锐': 'R', '仁': 'R', '然': 'R',
      // S
      '三': 'S', '山': 'S', '上': 'S', '深': 'S', '生': 'S', '市': 'S', '世': 'S', '首': 'S', '水': 'S', '四': 'S', '苏': 'S', '孙': 'S', '盛': 'S', '胜': 'S', '圣': 'S', '升': 'S', '声': 'S', '石': 'S', '时': 'S', '实': 'S', '十': 'S', '食': 'S', '史': 'S', '事': 'S', '室': 'S', '书': 'S', '树': 'S', '双': 'S', '顺': 'S', '思': 'S', '斯': 'S', '司': 'S', '丝': 'S', '松': 'S', '宋': 'S', '速': 'S', '素': 'S',
      // T
      '太': 'T', '天': 'T', '田': 'T', '通': 'T', '土': 'T', '泰': 'T', '台': 'T', '唐': 'T', '堂': 'T', '腾': 'T', '特': 'T', '铁': 'T', '同': 'T', '统': 'T', '投': 'T', '图': 'T', '团': 'T',
      // W
      '万': 'W', '王': 'W', '为': 'W', '文': 'W', '我': 'W', '五': 'W', '武': 'W', '伟': 'W', '维': 'W', '威': 'W', '微': 'W', '未': 'W', '位': 'W', '温': 'W', '闻': 'W', '问': 'W', '无': 'W', '吴': 'W', '物': 'W', '务': 'W', '悟': 'W', '雾': 'W',
      // X
      '西': 'X', '下': 'X', '先': 'X', '现': 'X', '小': 'X', '新': 'X', '星': 'X', '学': 'X', '雪': 'X', '血': 'X', '寻': 'X', '迅': 'X', '讯': 'X', '训': 'X', '信': 'X', '心': 'X', '欣': 'X', '辛': 'X', '兴': 'X', '行': 'X', '形': 'X', '型': 'X', '性': 'X', '姓': 'X', '幸': 'X', '修': 'X', '秀': 'X', '休': 'X', '虚': 'X', '需': 'X', '许': 'X', '续': 'X', '选': 'X', '宣': 'X', '旋': 'X', '学': 'X',
      // Y
      '亚': 'Y', '阳': 'Y', '一': 'Y', '银': 'Y', '永': 'Y', '有': 'Y', '友': 'Y', '于': 'Y', '元': 'Y', '园': 'Y', '远': 'Y', '月': 'Y', '云': 'Y', '运': 'Y', '营': 'Y', '英': 'Y', '影': 'Y', '应': 'Y', '用': 'Y', '优': 'Y', '由': 'Y', '游': 'Y', '油': 'Y', '右': 'Y', '又': 'Y', '与': 'Y', '语': 'Y', '雨': 'Y', '育': 'Y', '域': 'Y', '玉': 'Y', '预': 'Y', '原': 'Y', '源': 'Y', '院': 'Y', '愿': 'Y', '约': 'Y', '越': 'Y', '跃': 'Y', '岳': 'Y', '悦': 'Y', '阅': 'Y', '乐': 'Y', '药': 'Y', '要': 'Y', '也': 'Y', '业': 'Y', '页': 'Y', '夜': 'Y', '叶': 'Y', '一': 'Y', '医': 'Y', '依': 'Y', '移': 'Y', '易': 'Y', '益': 'Y', '意': 'Y', '义': 'Y', '艺': 'Y', '议': 'Y', '异': 'Y', '翼': 'Y', '因': 'Y', '音': 'Y', '阴': 'Y', '银': 'Y', '引': 'Y', '印': 'Y', '英': 'Y', '迎': 'Y', '赢': 'Y', '影': 'Y', '硬': 'Y', '映': 'Y', '用': 'Y', '邮': 'Y', '尤': 'Y', '由': 'Y', '友': 'Y', '有': 'Y', '又': 'Y', '右': 'Y', '幼': 'Y', '诱': 'Y', '于': 'Y', '余': 'Y', '鱼': 'Y', '愉': 'Y', '渝': 'Y', '榆': 'Y', '虞': 'Y', '与': 'Y', '宇': 'Y', '语': 'Y', '羽': 'Y', '雨': 'Y', '域': 'Y', '育': 'Y', '狱': 'Y', '浴': 'Y', '预': 'Y', '豫': 'Y', '遇': 'Y', '御': 'Y', '欲': 'Y', '元': 'Y', '园': 'Y', '原': 'Y', '圆': 'Y', '源': 'Y', '远': 'Y', '苑': 'Y', '愿': 'Y', '怨': 'Y', '院': 'Y', '曰': 'Y', '约': 'Y', '越': 'Y', '跃': 'Y', '钥': 'Y', '岳': 'Y', '粤': 'Y', '月': 'Y', '悦': 'Y', '阅': 'Y', '耘': 'Y', '云': 'Y', '郧': 'Y', '匀': 'Y', '陨': 'Y', '允': 'Y', '运': 'Y', '蕴': 'Y', '酝': 'Y', '晕': 'Y', '韵': 'Y', '孕': 'Y', '匝': 'Y', '杂': 'Y', '栽': 'Y', '哉': 'Y', '灾': 'Y', '宰': 'Y', '载': 'Y', '再': 'Y', '在': 'Y', '咱': 'Y', '攒': 'Y', '暂': 'Y', '赞': 'Y', '脏': 'Y', '葬': 'Y', '遭': 'Y', '糟': 'Y', '凿': 'Y', '藻': 'Y', '枣': 'Y', '早': 'Y', '澡': 'Y', '蚤': 'Y', '躁': 'Y', '噪': 'Y', '造': 'Y', '皂': 'Y', '灶': 'Y', '燥': 'Y', '责': 'Y', '择': 'Y', '则': 'Y', '泽': 'Y', '贼': 'Y', '怎': 'Y', '增': 'Y', '憎': 'Y', '曾': 'Y', '赠': 'Y', '扎': 'Y', '喳': 'Y', '渣': 'Y', '札': 'Y', '轧': 'Y', '铡': 'Y', '闸': 'Y', '眨': 'Y', '栅': 'Y', '榨': 'Y', '咋': 'Y', '乍': 'Y', '炸': 'Y', '诈': 'Y', '摘': 'Y', '斋': 'Y', '宅': 'Y', '窄': 'Y', '债': 'Y', '寨': 'Y', '瞻': 'Y', '毡': 'Y', '詹': 'Y', '粘': 'Y', '沾': 'Y', '盏': 'Y', '斩': 'Y', '辗': 'Y', '崭': 'Y', '展': 'Y', '蘸': 'Y', '栈': 'Y', '占': 'Y', '战': 'Y', '站': 'Y', '湛': 'Y', '绽': 'Y', '樟': 'Y', '章': 'Y', '彰': 'Y', '漳': 'Y', '张': 'Y', '掌': 'Y', '涨': 'Y', '杖': 'Y', '丈': 'Y', '帐': 'Y', '账': 'Y', '仗': 'Y', '胀': 'Y', '瘴': 'Y', '障': 'Y', '招': 'Y', '昭': 'Y', '找': 'Y', '沼': 'Y', '赵': 'Y', '照': 'Y', '罩': 'Y', '兆': 'Y', '肇': 'Y', '召': 'Y', '遮': 'Y', '折': 'Y', '哲': 'Y', '蛰': 'Y', '辙': 'Y', '者': 'Y', '锗': 'Y', '蔗': 'Y', '这': 'Y', '浙': 'Y', '珍': 'Y', '斟': 'Y', '真': 'Y', '甄': 'Y', '砧': 'Y', '臻': 'Y', '贞': 'Y', '针': 'Y', '侦': 'Y', '枕': 'Y', '疹': 'Y', '诊': 'Y', '震': 'Y', '振': 'Y', '镇': 'Y', '阵': 'Y', '蒸': 'Y', '挣': 'Y', '睁': 'Y', '征': 'Y', '狰': 'Y', '争': 'Y', '怔': 'Y', '整': 'Y', '拯': 'Y', '正': 'Y', '政': 'Y', '帧': 'Y', '症': 'Y', '郑': 'Y', '证': 'Y', '芝': 'Y', '枝': 'Y', '支': 'Y', '吱': 'Y', '蜘': 'Y', '知': 'Y', '肢': 'Y', '脂': 'Y', '汁': 'Y', '之': 'Y', '织': 'Y', '职': 'Y', '直': 'Y', '植': 'Y', '殖': 'Y', '执': 'Y', '值': 'Y', '侄': 'Y', '址': 'Y', '指': 'Y', '止': 'Y', '趾': 'Y', '只': 'Y', '旨': 'Y', '纸': 'Y', '志': 'Y', '挚': 'Y', '掷': 'Y', '至': 'Y', '致': 'Y', '置': 'Y', '帜': 'Y', '峙': 'Y', '制': 'Y', '智': 'Y', '秩': 'Y', '稚': 'Y', '质': 'Y', '炙': 'Y', '痔': 'Y', '滞': 'Y', '治': 'Y', '窒': 'Y', '中': 'Y', '盅': 'Y', '忠': 'Y', '钟': 'Y', '衷': 'Y', '终': 'Y', '种': 'Y', '肿': 'Y', '重': 'Y', '仲': 'Y', '众': 'Y', '舟': 'Y', '周': 'Y', '州': 'Y', '洲': 'Y', '诌': 'Y', '粥': 'Y', '轴': 'Y', '肘': 'Y', '帚': 'Y', '咒': 'Y', '皱': 'Y', '宙': 'Y', '昼': 'Y', '骤': 'Y', '珠': 'Y', '株': 'Y', '蛛': 'Y', '朱': 'Y', '猪': 'Y', '诸': 'Y', '诛': 'Y', '逐': 'Y', '竹': 'Y', '烛': 'Y', '煮': 'Y', '拄': 'Y', '瞩': 'Y', '嘱': 'Y', '主': 'Y', '著': 'Y', '柱': 'Y', '助': 'Y', '蛀': 'Y', '贮': 'Y', '铸': 'Y', '筑': 'Y', '住': 'Y', '注': 'Y', '祝': 'Y', '驻': 'Y', '抓': 'Y', '爪': 'Y', '拽': 'Y', '专': 'Y', '砖': 'Y', '转': 'Y', '撰': 'Y', '赚': 'Y', '篆': 'Y', '桩': 'Y', '庄': 'Y', '装': 'Y', '妆': 'Y', '撞': 'Y', '壮': 'Y', '状': 'Y', '椎': 'Y', '锥': 'Y', '追': 'Y', '赘': 'Y', '坠': 'Y', '缀': 'Y', '谆': 'Y', '准': 'Y', '捉': 'Y', '拙': 'Y', '卓': 'Y', '桌': 'Y', '琢': 'Y', '茁': 'Y', '酌': 'Y', '啄': 'Y', '着': 'Y', '灼': 'Y', '浊': 'Y', '兹': 'Y', '咨': 'Y', '资': 'Y', '姿': 'Y', '滋': 'Y', '淄': 'Y', '孜': 'Y', '紫': 'Y', '仔': 'Y', '籽': 'Y', '滓': 'Y', '子': 'Y', '自': 'Y', '渍': 'Y', '字': 'Y', '鬃': 'Y', '棕': 'Y', '踪': 'Y', '宗': 'Y', '综': 'Y', '总': 'Y', '纵': 'Y', '邹': 'Y', '走': 'Y', '奏': 'Y', '揍': 'Y', '租': 'Y', '足': 'Y', '卒': 'Y', '族': 'Y', '祖': 'Y', '诅': 'Y', '阻': 'Y', '组': 'Y', '钻': 'Y', '纂': 'Y', '嘴': 'Y', '醉': 'Y', '最': 'Y', '罪': 'Y', '尊': 'Y', '遵': 'Y', '昨': 'Y', '左': 'Y', '佐': 'Y', '柞': 'Y', '做': 'Y', '作': 'Y', '坐': 'Y', '座': 'Y',
      // Z
      '在': 'Z', '张': 'Z', '赵': 'Z', '中': 'Z', '周': 'Z', '主': 'Z', '紫': 'Z', '总': 'Z', '最': 'Z', '早': 'Z', '造': 'Z', '增': 'Z', '展': 'Z', '战': 'Z', '站': 'Z', '正': 'Z', '政': 'Z', '知': 'Z', '直': 'Z', '制': 'Z', '智': 'Z', '质': 'Z', '治': 'Z', '重': 'Z', '众': 'Z', '州': 'Z', '专': 'Z', '转': 'Z', '装': 'Z', '状': 'Z', '准': 'Z', '资': 'Z', '自': 'Z', '宗': 'Z', '走': 'Z', '组': 'Z', '作': 'Z', '左': 'Z', '做': 'Z'
    };

    // 先查找精确匹配
    if (pinyinMap[firstChar]) {
      return pinyinMap[firstChar];
    }

    // 如果没有精确匹配，使用更精确的Unicode编码范围判断
    const code = firstChar.charCodeAt(0);
    if (code >= 19968 && code <= 40869) {
      // 中文字符范围，使用更精确的Unicode范围映射
      if (code >= 19968 && code <= 20105) return 'A';
      if (code >= 20106 && code <= 21319) return 'B';
      if (code >= 21320 && code <= 22763) return 'C';
      if (code >= 22764 && code <= 24217) return 'D';
      if (code >= 24218 && code <= 24907) return 'E';
      if (code >= 24908 && code <= 25242) return 'F';
      if (code >= 25243 && code <= 25915) return 'G';
      if (code >= 25916 && code <= 26679) return 'H';
      if (code >= 26680 && code <= 27394) return 'J';
      if (code >= 27395 && code <= 28062) return 'K';
      if (code >= 28063 && code <= 28895) return 'L';
      if (code >= 28896 && code <= 29715) return 'M';
      if (code >= 29716 && code <= 30422) return 'N';
      if (code >= 30423 && code <= 31665) return 'O';
      if (code >= 31666 && code <= 32677) return 'P';
      if (code >= 32678 && code <= 33575) return 'Q';
      if (code >= 33576 && code <= 34249) return 'R';
      if (code >= 34250 && code <= 35244) return 'S';
      if (code >= 35245 && code <= 36281) return 'T';
      if (code >= 36282 && code <= 37322) return 'W';
      if (code >= 37323 && code <= 38263) return 'X';
      if (code >= 38264 && code <= 39204) return 'Y';
      if (code >= 39205 && code <= 40869) return 'Z';
    }

    return '#';
  },

  // 设置当前选中的楼宇
  setSelectedBuilding() {
    // 这个方法现在不需要了，因为我们直接点击选择
  },

  // 搜索输入
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });

    // 实时搜索：当输入内容时自动搜索，清空时恢复全部楼宇
    if (keyword.trim()) {
      // 防抖处理，避免频繁搜索
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.performSearch(keyword.trim());
      }, 300);
    } else {
      // 输入框为空时，恢复到全部楼宇
      this.resetToAllBuildings();
    }
  },

  // 执行搜索
  onSearch() {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) {
      this.resetToAllBuildings();
      return;
    }
    this.performSearch(keyword);
  },

  // 清空搜索框
  clearSearch() {
    this.setData({
      searchKeyword: ''
    });
    // 清空搜索框后恢复全部楼宇
    this.resetToAllBuildings();
  },

  // 重置到全部楼宇状态
  resetToAllBuildings() {
    // 重新按首字母排序显示全部楼宇
    const sortedList = this.sortBuildingsByAlphabet(this.data.buildingList);
    this.setData({
      filteredBuildingList: sortedList
    });
  },

  // 执行搜索逻辑
  performSearch(keyword) {
    const buildingList = this.data.buildingList;
    if (!buildingList || buildingList.length === 0) {
      return;
    }

    // 模糊搜索：搜索楼宇名称和地址
    const filteredBuildings = buildingList.filter(building => {
      if (!building) return false;

      const name = building.name || '';
      const address = building.address || '';

      // 转换为小写进行不区分大小写的搜索
      const searchKeyword = keyword.toLowerCase();
      const buildingName = name.toLowerCase();
      const buildingAddress = address.toLowerCase();

      return buildingName.includes(searchKeyword) || buildingAddress.includes(searchKeyword);
    });

    // 对搜索结果按首字母排序
    const sortedList = this.sortBuildingsByAlphabet(filteredBuildings);
    this.setData({
      filteredBuildingList: sortedList
    });
  },

  // 选择楼宇（先更新选中状态，再返回）
  onBuildingSelect(e) {
    const building = e.currentTarget.dataset.building;

    // 立即更新选中状态，让用户看到选中反馈
    this.setData({
      selectedBuildingId: building.id
    });

    // 保存选中的楼宇到本地存储（隐藏距离信息）
    wx.setStorageSync('selectedBuildingId', building.id);
    wx.setStorageSync('selectedBuildingName', building.name);
    wx.setStorageSync('selectedBuildingDistance', ''); // 不保存距离信息

    // 触发全局事件通知其他页面更新
    const eventChannel = this.getOpenerEventChannel();
    if (eventChannel) {
      eventChannel.emit('buildingSelected', building);
    }

    // 显示选择成功提示
    // Toast({
    //   context: this,
    //   selector: '#t-toast',
    //   message: `已选择：${building.name}`,
    //   icon: 'check-circle',
    //   duration: 1000
    // });

    // 延迟返回，让用户看到选中状态和提示
    setTimeout(() => {
      wx.navigateBack();
    }, 400);
  },

  // 页面卸载时清理定时器
  onUnload() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }
});
