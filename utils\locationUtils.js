/**
 * 位置相关工具方法
 */

/**
 * 计算两个经纬度之间的距离（使用Haversine公式）
 * @param {number} lat1 - 第一个点的纬度
 * @param {number} lng1 - 第一个点的经度
 * @param {number} lat2 - 第二个点的纬度
 * @param {number} lng2 - 第二个点的经度
 * @returns {number} 距离（单位：米）
 */
function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371000; // 地球半径，单位：米
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

/**
 * 获取当前位置
 * @returns {Promise} 返回包含经纬度的Promise对象
 */
function getCurrentLocation() {
  return new Promise((resolve, reject) => {
    // 先检查权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation']) {
          // 已授权，直接获取位置
          getLocationData(resolve, reject);
        } else {
          // 未授权，申请权限
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => {
              getLocationData(resolve, reject);
            },
            fail: (err) => {
              console.warn('用户拒绝授权位置信息:', err);
              reject(new Error('用户拒绝授权位置信息'));
            }
          });
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

/**
 * 获取位置数据的内部方法
 */
function getLocationData(resolve, reject) {
  wx.getLocation({
    type: 'gcj02', // 返回可以用于wx.openLocation的经纬度
    success: (res) => {
      resolve({
        latitude: res.latitude,
        longitude: res.longitude
      });
    },
    fail: (err) => {
      console.error('获取位置失败:', err);
      reject(new Error('获取位置失败: ' + err.errMsg));
    }
  });
}

/**
 * 根据当前位置找到最近的楼宇
 * @param {Array} bcList - 楼宇列表，每个楼宇对象需要包含name和remark字段
 * @param {Object} currentLocation - 当前位置 {latitude, longitude}，可选参数
 * @returns {Promise} 返回最近楼宇的Promise对象
 */
async function findNearestBuilding(bcList, currentLocation = null) {
  try {
    // 如果没有传入当前位置，则获取当前位置
    if (!currentLocation) {
      currentLocation = await getCurrentLocation();
    }

    if (!bcList || !Array.isArray(bcList) || bcList.length === 0) {
      throw new Error('楼宇列表为空或格式错误');
    }

    let nearestBuilding = null;
    let minDistance = Infinity;

    // 遍历楼宇列表，计算距离
    bcList.forEach(building => {
      if (!building) {
        console.warn('楼宇数据为空');
        return;
      }
      if (!building.remark) {
        console.warn('楼宇缺少经纬度信息:', building.name);
        return;
      }

      try {
        // 解析经纬度，remark格式为['12313','22334']
        let coordinates;
        if (typeof building.remark === 'string') {
          coordinates = JSON.parse(building.remark);
        } else if (Array.isArray(building.remark)) {
          coordinates = building.remark;
        } else {
          console.warn('楼宇经纬度格式错误:', building.name, building.remark);
          return;
        }

        if (!Array.isArray(coordinates) || coordinates.length < 2) {
          console.warn('楼宇经纬度格式错误:', building.name, building.remark);
          return;
        }

        const buildingLng = parseFloat(coordinates[0]);
        const buildingLat = parseFloat(coordinates[1]);

        if (isNaN(buildingLng) || isNaN(buildingLat)) {
          console.warn('楼宇经纬度无法解析:', building.name, building.remark);
          return;
        }

        // 计算距离
        const distance = calculateDistance(
          currentLocation.latitude,
          currentLocation.longitude,
          buildingLat,
          buildingLng
        );

        // 更新最近的楼宇
        if (distance < minDistance) {
          minDistance = distance;
          nearestBuilding = {
            ...building,
            distance: distance,
            distanceText: distance < 1000 ? `${Math.round(distance)}米` : `${(distance / 1000).toFixed(1)}公里`
          };
        }
      } catch (error) {
        // console.warn('处理楼宇数据时出错:', building.name, error);
      }
    });

    if (!nearestBuilding) {
      throw new Error('未找到有效的楼宇数据');
    }

    return nearestBuilding;
  } catch (error) {
    console.error('查找最近楼宇时出错:', error);
    throw error;
  }
}

/**
 * 打印最近的楼宇信息
 * @param {Array} bcList - 楼宇列表
 * @param {Object} currentLocation - 当前位置，可选参数
 */
async function printNearestBuilding(bcList, currentLocation = null) {
  try {
    const nearestBuilding = await findNearestBuilding(bcList, currentLocation);
    console.log('离我最近的楼宇:', nearestBuilding.name);
    console.log('距离:', nearestBuilding.distanceText);
    console.log('楼宇详细信息:', nearestBuilding);
    return nearestBuilding;
  } catch (error) {
    console.error('获取最近楼宇失败:', error.message);
    throw error;
  }
}

module.exports = {
  calculateDistance,
  getCurrentLocation,
  getLocationData,
  findNearestBuilding,
  printNearestBuilding
};
