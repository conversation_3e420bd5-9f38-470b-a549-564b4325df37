/* pages/my/address/edit/editAddress.wxss */
page {
  background-color: #f5f5f5;
}

page .divider-line {
  width: 100%;
  height: 20rpx;
  background-color: #f5f5f5;
}

.form-address .form-content {
  --td-input-vertical-padding: 0;
}

.address-textarea {
  --td-input-disabled-text-color: #333;
  --td-text-color-disabled: #333;
  /* var(--td-input-disabled-text-color, var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))) */
}

.form-cell .t-cell__title {
  width: 144rpx;
  padding-right: 32rpx;
  flex: none !important;
}

.submit_btn {
  box-sizing: border-box;
  padding: 64rpx 30rpx 88rpx 30rpx;
}


/* .submit_btn {
  height: 80rpx;
  line-height: 80rpx;
  background-color: var(--themeColor);
  margin-top: 60rpx;
  border-radius: 50rpx;
  text-align: center;
  font-size: 30rpx;
  color: white;
} */

.textarea__wrapper {
  width: 100%;
  box-sizing: border-box;
  padding-left: 24rpx; /* 与“手机号”一栏输入文字起始位置对齐 */
}

.textarea__wrapper .t-textarea {
  padding: 0 !important;
  /* 确保与其他输入框垂直对齐 */
  display: flex;
  align-items: center;
  min-height: 48rpx;
}
/* 调整textarea内部元素对齐 */
.textarea__wrapper .t-textarea .t-textarea__inner {
  display: flex;
  align-items: center;
  min-height: 48rpx;
  line-height: 1.4;
}

.textarea__wrapper .t-textarea textarea {
  line-height: 1.4 !important;
  padding: 0 !important;
  margin: 0 !important;
  vertical-align: middle;
}

/* 详细地址输入框字体调整为28rpx */
.textarea__wrapper .t-textarea .t-textarea__inner {
  font-size: 28rpx !important;
}

/* 备用选择器，确保字体生效 */
.textarea__wrapper .t-textarea textarea {
  font-size: 28rpx !important;
}

/* 更广泛的选择器，覆盖TDesign默认样式 */
.textarea__wrapper textarea,
.textarea__wrapper .t-textarea__content,
.textarea__wrapper .t-input__content {
  font-size: 28rpx !important;
}

/* placeholder文字字体大小也调整到28rpx */
.textarea__wrapper .t-textarea textarea::placeholder,
.textarea__wrapper textarea::placeholder {
  font-size: 28rpx !important;
}
/* 增大定位按钮点击区域 */
.location-btn {
  padding: 20rpx;
  margin: -20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}


.header {
  width: 100vw;
  display: flex;
  align-items: center;
  height: 116rpx;
}

.title {
  flex: 1;
  text-align: center;
  font-weight: 600;
  font-size: 36rpx;
  color: var(--td-text-color-primary);
}

.btn--cancel {
  font-size: 32rpx;
  padding: 32rpx;
  color: #333;
  position: absolute;
  left: 0;
}

.bc-list {}

.bc-list-item {
  padding: 24rpx 30rpx;
  font-size: 32rpx;
}

.bc-list-item:hover {
  background-color: #f5f5f5;
}