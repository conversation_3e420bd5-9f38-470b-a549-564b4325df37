<!--pages/my/address/edit/editAddress.wxml-->
<view>
  <view class="divider-line" />
  <t-cell title="获取微信地址" bind:tap="getWxLocation">
    <t-icon slot="left-icon" color="#0ABF5B" name="logo-wechat-stroke-filled" size="48rpx" />
    <t-icon slot="right-icon" name="chevron-right" color="#bbb" />
  </t-cell>
  <view class="divider-line" />

  <view class="form-address">
    <form class="form-content">
      <t-cell-group>
        <t-cell class="form-cell" title="联系人">
          <t-input data-item="userName" slot="note" borderless maxlength="20" type="text" value="{{form.userName}}" placeholder="您的姓名" bind:change="onInputValue" />
        </t-cell>
        <t-cell class="form-cell" title="手机号">
          <t-input data-item="telNumber" slot="note" borderless type="number" value="{{form.telNumber}}" maxlength="11" placeholder="联系您的手机号" bind:change="onInputValue" />
        </t-cell>
        <!-- 地区字段已隐藏，但省市区数据仍会传给接口 -->
        <t-cell class="form-cell" title="详细地址" bordered="{{false}}">
          <view slot="note" class="textarea__wrapper">
            <t-textarea data-item="detailInfo" slot="note" type="text" value="{{form.detailInfo}}" placeholder="您的详细地址" autosize bind:change="onInputValue" data-item="detailInfo" />
          </view>
          <view slot="right-icon" class="location-btn" catch:tap="fillDetailFromLocation">
            <t-icon name="location" color="var(--themeColor)" />
          </view>
        </t-cell>
        <view class="divider-line" />
        <t-cell title="所属楼宇" bordered="{{false}}" note="{{currentBuildingName || '未选择楼宇'}}"></t-cell>
        <view class="divider-line" />
        <t-cell title="设置为默认" bordered="{{false}}">
          <t-switch value="{{form.defaultFlag}}" custom-value="{{[1,0]}}" slot="note" bind:change="onCheckDefaultAddress" />
        </t-cell>
      </t-cell-group>
      <!-- <view style="margin: 40rpx 40rpx 0rpx 40rpx;" class="submit_btn" bind:tap="formSubmit">保 存</view> -->
      <view class="submit_btn">
        <t-button shape="round" block disabled="{{!submitActive}}" bind:tap="formSubmit">保 存</t-button>
      </view>
    </form>
  </view>

  <!-- 地区选择器已移除，省市区通过定位自动获取 -->



  <t-toast id="t-toast" />
</view>