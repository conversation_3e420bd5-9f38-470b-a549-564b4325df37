// pages/my/address/edit/editAddress.js
import { getPermission } from '~/utils/getPermission';
import Toast from 'tdesign-miniprogram/toast/index';
import { areaData } from '~/config/areaData';
import { userAddressAdd, userAddressUpdate, userAddressInfo } from '~/api/UserAddressApi';
import BCApi from '~/api/BCApi';
import { printNearestBuilding } from '~/utils/locationUtils';

const innerPhoneReg = '^1(?:3\\d|4[4-9]|5[0-35-9]|6[67]|7[0-8]|8\\d|9\\d)\\d{8}$';// 手机号
const innerPhoneReg2 = '^(0[0-9]{2,3}\-)?([2-9][0-9]{6,7})+(\-[0-9]{1,4})?$';// 固定电话
const innerNameReg = '^[a-zA-Z\\d\\u4e00-\\u9fa5]+$';

Page({
  data: {
    areaData: areaData,
    areaPickerVisible: false,
    form: {
      id: '',
      bcId: '',
      provinceName: '',
      cityName: '',
      countyName: '',
      detailInfo: '',
      telNumber: '',
      userName: '',
      defaultFlag: 0
    },
    submitActive: false,
    selectedAreaData: [],
    bcList: [],
    currentBuildingName: '',
    currentBuildingId: '',
  },
  hasSava: false,
  privateData: { verifyTips: '' },
  onLoad(options) {
    // 获取首页选择的楼宇信息
    this.initBuildingInfo();

    if (options.id) {
      this.setData({ "form.id": options.id });
      this.init(true);
    }
    // BCApi.BCList().then(res => {
    //   this.setData({ bcList: res.records });
    //   // 查找并打印离我最近的楼宇
    //   this.findAndPrintNearestBuilding(res.records);
    // });
  },
  onShow() {
    // 每次显示页面时更新楼宇信息
    this.initBuildingInfo();
  },

  // 初始化楼宇信息
  initBuildingInfo() {
    // 从本地存储获取首页选择的楼宇信息
    const selectedBuildingId = wx.getStorageSync('selectedBuildingId') || '';
    const selectedBuildingName = wx.getStorageSync('selectedBuildingName') || '';

    if (selectedBuildingName) {
      this.setData({
        currentBuildingName: selectedBuildingName,
        currentBuildingId: selectedBuildingId,
        'form.bcId': selectedBuildingId
      });

      // 验证表单合法性
      const { isLegal, tips } = this.onVerifyInputLegal();
      this.setData({ submitActive: isLegal });
      this.privateData.verifyTips = tips;
    }
  },
  // 查找并打印离我最近的楼宇
  async findAndPrintNearestBuilding(bcList) {
    try {
      const nearestBuilding = await printNearestBuilding(bcList);
      console.log('成功找到最近的楼宇:', nearestBuilding.name);
    } catch (error) {
      console.log('查找最近楼宇失败:', error.message);
    }
  },

  getWxLocation() {
    getPermission({ code: 'scope.address', name: '收货地址' }).then(() => {
      wx.chooseAddress({
        success: async (options) => {
          console.log(options)
          const { provinceName, cityName, countyName, detailInfo, userName, telNumber } = options;
          this.setData({
            "form.provinceName": provinceName,
            "form.cityName": cityName,
            "form.countyName": countyName,
            "form.detailInfo": detailInfo,
            "form.userName": userName,
            "form.telNumber": telNumber
          }, () => {
            const { isLegal, tips } = this.onVerifyInputLegal();
            this.setData({ submitActive: isLegal });
            this.privateData.verifyTips = tips;
          });
        },
        fail(err) {
          console.warn('未选择微信收货地址', err);
        },
      });
    });
  },

	  fillDetailFromLocation() {
	    // 通过定位获取附近地址并填充到“详细地址”，支持手动修改
	    getPermission({ code: 'scope.userLocation', name: '地址位置' }).then(() => {
	      wx.getLocation({
	        type: 'gcj02',
	        success: () => {
	          // 使用内置选择地点，拿到可读的地址文本
	          wx.chooseLocation({
	            success: (res) => {
	              console.log('chooseLocation result:', res);

	              // 解析地址信息
	              const addressInfo = this.parseAddressFromLocation(res);

	              // 更新表单数据
	              this.setData({
	                'form.provinceName': addressInfo.province,
	                'form.cityName': addressInfo.city,
	                'form.countyName': addressInfo.district,
	                'form.detailInfo': addressInfo.detail
	              }, () => {
	                const { isLegal, tips } = this.onVerifyInputLegal();
	                this.setData({ submitActive: isLegal });
	                this.privateData.verifyTips = tips;
	              });
	            },
	            fail: (err) => {
	              console.warn('wx.chooseLocation fail:', err);
	              if (err.errMsg !== 'chooseLocation:fail cancel') {
	                Toast({ context: this, selector: '#t-toast', message: '地点错误，请重新选择', icon: '', duration: 1000 });
	              }
	            }
	          });
	        },
	        fail: (err) => {
	          console.warn('获取定位失败', err);
	          Toast({ context: this, selector: '#t-toast', message: '定位失败，请检查定位权限', icon: '', duration: 1000 });
	        }
	      });
	    });
	  },
	  // 解析地址信息，从chooseLocation返回的地址中提取省市区
	  parseAddressFromLocation(locationResult) {
	    const { address = '', name = '' } = locationResult;

	    // 组合详细地址
	    const detail = (name && address) ? `${address} ${name}` : (address || name || '');

	    // 尝试从address中解析省市区
	    let province = '', city = '', district = '';

	    if (address) {
	      console.log('原始地址:', address);

	      // 按顺序逐级解析，避免重复匹配
	      let remainingAddress = address;

	      // 1. 提取省份（包含省、自治区、特别行政区、直辖市）
	      const provinceRegex = /^(.*?(?:省|自治区|特别行政区|市))/;
	      const provinceMatch = remainingAddress.match(provinceRegex);
	      if (provinceMatch) {
	        province = provinceMatch[1];
	        remainingAddress = remainingAddress.substring(province.length);
	        console.log('解析省份:', province, '剩余:', remainingAddress);
	      }

	      // 2. 提取市（从剩余地址中提取）
	      if (remainingAddress) {
	        const cityRegex = /^(.*?(?:市|州|盟|地区))/;
	        const cityMatch = remainingAddress.match(cityRegex);
	        if (cityMatch) {
	          city = cityMatch[1];
	          remainingAddress = remainingAddress.substring(city.length);
	          console.log('解析城市:', city, '剩余:', remainingAddress);
	        }
	      }

	      // 3. 提取区县（从剩余地址中提取）
	      if (remainingAddress) {
	        const districtRegex = /^(.*?(?:区|县|市|旗|镇))/;
	        const districtMatch = remainingAddress.match(districtRegex);
	        if (districtMatch) {
	          district = districtMatch[1];
	          console.log('解析区县:', district);
	        }
	      }

	      console.log('最终解析结果:', { province, city, district });
	    }

	    return {
	      province: province || '',
	      city: city || '',
	      district: district || '',
	      detail: detail
	    };
	  },

  async init() {
    try {
      const result = await userAddressInfo(this.data.form.id);
      this.setData({ form: result, submitActive: true });
    } catch (error) {
      console.error('err:', error);
    }
  },
  formSubmit() {
    const { submitActive } = this.data;
    if (!submitActive) {
      Toast({ context: this, selector: '#t-toast', message: this.privateData.verifyTips, icon: '', duration: 1000 });
      return;
    }
    const { form } = this.data;
    this.hasSava = true;
    wx.showLoading({ title: '正在保存...', mask: true });
    if (this.data.form.id) {
      userAddressUpdate(form).then(res => {
        this.hasSava = false;
        console.log(res);
        wx.hideLoading();
        wx.navigateBack();
      }).catch(error => {
        this.hasSava = false;
        console.log(error);
        wx.hideLoading();
      });
    } else {
      userAddressAdd(form).then(res => {
        this.hasSava = false;
        console.log(res);
        wx.hideLoading();
        wx.navigateBack();
      }).catch(error => {
        this.hasSava = false;
        console.log(error);
        wx.hideLoading();
      });
    }
  },
  onInputValue(e) {
    const { item } = e.currentTarget.dataset;
    // 移除了地区选择器相关逻辑，现在只处理其他输入字段
    const { value = '' } = e.detail;
    this.setData({ [`form.${item}`]: value },
      () => {
        const { isLegal, tips } = this.onVerifyInputLegal();
        this.setData({ submitActive: isLegal });
        this.privateData.verifyTips = tips;
      },
    );
  },
  onCheckDefaultAddress({ detail }) {
    const { value } = detail;
    this.setData({ 'form.defaultFlag': value });
  },
  // onPickArea() {
  //   this.setData({ areaPickerVisible: true });
  // },
  onVerifyInputLegal() {
    const { bcId, userName, telNumber, detailInfo } = this.data.form;
    const nameRegExp = new RegExp(String(innerNameReg));
    const phoneRegExp = new RegExp(String(innerPhoneReg));
    const phoneRegExp2 = new RegExp(String(innerPhoneReg2));

    if (!userName || !userName.trim()) { return { isLegal: false, tips: '请填写收货人' }; }
    if (!nameRegExp.test(userName)) { return { isLegal: false, tips: '收货人仅支持输入中文、英文（区分大小写）、数字' }; }
    if (!telNumber || !telNumber.trim()) { return { isLegal: false, tips: '请填写手机号' }; }
    if (!phoneRegExp.test(telNumber) && !phoneRegExp2.test(telNumber)) { return { isLegal: false, tips: '请填写正确的手机号' }; }
    // 省市区验证改为可选，因为用户界面已隐藏地区选择，主要通过定位获取
    // if (!provinceName || !provinceName.trim() || !cityName || !cityName.trim() || !countyName || !countyName.trim()) {
    //   return { isLegal: false, tips: '请选择省市区信息' };
    // }
    if (!bcId || !bcId.trim()) { return { isLegal: false, tips: '请选择所属楼宇' }; }
    if (!detailInfo || !detailInfo.trim()) { return { isLegal: false, tips: '请完善详细地址' }; }
    if (detailInfo && detailInfo.trim().length > 50) { return { isLegal: false, tips: '详细地址不能超过50个字符' }; }

    return { isLegal: true, tips: '添加成功' };
  },
  onSearchAddress() {
    getPermission({ code: 'scope.userLocation', name: '地址位置' }).then(() => {
      wx.chooseLocation({
        success: (res) => {
          if (res.name) {
            this.triggerEvent('addressParse', {
              address: res.address,
              name: res.name,
              latitude: res.latitude,
              longitude: res.longitude,
            });
          } else {
            Toast({ context: this, selector: '#t-toast', message: this.privateData.verifyTips, icon: '', duration: 1000 });
          }
        },
        fail: (res) => {
          console.warn(`wx.chooseLocation fail: ${JSON.stringify(res.errMsg)}`);
          if (res.errMsg !== 'chooseLocation:fail cancel') {
            Toast({ context: this, selector: '#t-toast', message: '地点错误，请重新选择', icon: '', duration: 1000 });
          }
        },
      });
    });
  }
})