// 微信wx.getLocation审核问题的替代解决方案

// 方案1：仅使用wx.chooseLocation（推荐）
// 优点：无需定位权限，审核容易通过，用户体验良好
// 缺点：用户需要手动在地图上选择位置
fillDetailFromLocation_v1() {
  // 直接使用地址选择器，无需获取定位权限
  wx.chooseLocation({
    success: (res) => {
      console.log('chooseLocation result:', res);
      
      // 解析地址信息
      const addressInfo = this.parseAddressFromLocation(res);
      
      // 更新表单数据
      this.setData({
        'form.provinceName': addressInfo.province,
        'form.cityName': addressInfo.city,
        'form.countyName': addressInfo.district,
        'form.detailInfo': addressInfo.detail
      }, () => {
        const { isLegal, tips } = this.onVerifyInputLegal();
        this.setData({ submitActive: isLegal });
        this.privateData.verifyTips = tips;
      });
    },
    fail: (err) => {
      console.warn('wx.chooseLocation fail:', err);
      if (err.errMsg !== 'chooseLocation:fail cancel') {
        Toast({ context: this, selector: '#t-toast', message: '地点选择失败，请重试', icon: '', duration: 1000 });
      }
    }
  });
},

// 方案2：使用第三方地图API（如腾讯地图、高德地图）
// 优点：功能强大，可以获取精确位置
// 缺点：需要申请API密钥，增加开发复杂度
fillDetailFromLocation_v2() {
  // 使用腾讯地图API获取位置
  // 需要在app.json中配置permission
  wx.chooseLocation({
    success: (res) => {
      // 使用腾讯地图逆地理编码API获取详细地址
      this.getAddressFromTencentMap(res.latitude, res.longitude);
    },
    fail: (err) => {
      console.warn('选择位置失败:', err);
    }
  });
},

// 腾讯地图逆地理编码
getAddressFromTencentMap(lat, lng) {
  const key = 'YOUR_TENCENT_MAP_KEY'; // 需要申请腾讯地图API密钥
  
  wx.request({
    url: 'https://apis.map.qq.com/ws/geocoder/v1/',
    data: {
      location: `${lat},${lng}`,
      key: key,
      get_poi: 1
    },
    success: (res) => {
      if (res.data.status === 0) {
        const result = res.data.result;
        const addressInfo = {
          province: result.ad_info.province,
          city: result.ad_info.city,
          district: result.ad_info.district,
          detail: result.formatted_addresses.recommend
        };
        
        this.setData({
          'form.provinceName': addressInfo.province,
          'form.cityName': addressInfo.city,
          'form.countyName': addressInfo.district,
          'form.detailInfo': addressInfo.detail
        });
      }
    }
  });
},

// 方案3：手动输入 + 地址联想
// 优点：完全避免定位权限问题
// 缺点：用户体验稍差，需要手动输入
fillDetailFromLocation_v3() {
  // 显示地址输入弹窗
  wx.showModal({
    title: '输入地址',
    editable: true,
    placeholderText: '请输入详细地址',
    success: (res) => {
      if (res.confirm && res.content) {
        // 可以调用地址解析API来标准化地址
        this.parseManualAddress(res.content);
      }
    }
  });
},

// 解析手动输入的地址
parseManualAddress(address) {
  // 简单的地址解析逻辑
  const addressInfo = this.parseAddressFromText(address);
  
  this.setData({
    'form.provinceName': addressInfo.province,
    'form.cityName': addressInfo.city,
    'form.countyName': addressInfo.district,
    'form.detailInfo': address
  });
},

// 从文本中解析地址信息
parseAddressFromText(text) {
  // 使用正则表达式解析省市区
  let province = '', city = '', district = '';
  
  // 省份匹配
  const provinceMatch = text.match(/(.*?(?:省|自治区|特别行政区|市))/);
  if (provinceMatch) {
    province = provinceMatch[1];
  }
  
  // 城市匹配
  const cityMatch = text.match(/(.*?(?:市|州|盟|地区))/);
  if (cityMatch && cityMatch[1] !== province) {
    city = cityMatch[1];
  }
  
  // 区县匹配
  const districtMatch = text.match(/(.*?(?:区|县|市|旗|镇))/);
  if (districtMatch && districtMatch[1] !== city) {
    district = districtMatch[1];
  }
  
  return { province, city, district };
},

// 方案4：使用微信获取地址API（推荐用于电商场景）
// 优点：直接获取用户微信地址，用户体验最佳
// 缺点：需要用户授权，且只能获取已保存的地址
getWxAddress() {
  wx.chooseAddress({
    success: (res) => {
      console.log('微信地址:', res);
      
      this.setData({
        'form.userName': res.userName,
        'form.telNumber': res.telNumber,
        'form.provinceName': res.provinceName,
        'form.cityName': res.cityName,
        'form.countyName': res.countyName,
        'form.detailInfo': res.detailInfo
      }, () => {
        const { isLegal, tips } = this.onVerifyInputLegal();
        this.setData({ submitActive: isLegal });
        this.privateData.verifyTips = tips;
      });
    },
    fail: (err) => {
      console.warn('获取微信地址失败:', err);
      if (err.errMsg !== 'chooseAddress:fail cancel') {
        Toast({ context: this, selector: '#t-toast', message: '获取地址失败', icon: '', duration: 1000 });
      }
    }
  });
}
